import 'package:unstack/database/sql_database.dart';
import 'package:unstack/logic/streak/streak_contract.dart';
import 'package:unstack/models/streak/streak.model.dart';
import 'package:unstack/utils/app_logger.dart';

class StreakManager implements IStreakManagerContract {
  final DatabaseService _databaseService = DatabaseService.instance;

  @override
  Future<void> updateDayCompletion(
    int todayTasks,
    int completedTasks,
    bool allTasksCompleted,
  ) async {
    final today = DateTime.now();
    try {
      // Calculate current and longest streaks based on the new completion status
      final currentStreak =
          await _calculateCurrentStreak(today, allTasksCompleted);
      final existingLongestStreak =
          await _databaseService.getLongestStreakFromDB();
      final longestStreak = currentStreak > existingLongestStreak
          ? currentStreak
          : existingLongestStreak;

      final streakData = {
        'date': today.toIso8601String().split('T')[0],
        'totalTasks': todayTasks, // Only today's tasks
        'completedTasks': completedTasks, // Only today's completed tasks
        'allTasksCompleted': allTasksCompleted ? 1 : 0,
        'currentStreak': currentStreak,
        'longestStreak': longestStreak,
      };

      await _databaseService.insertOrUpdateStreakData(streakData);
      AppLogger.info(
          'Updated streak for ${today.toIso8601String().split('T')[0]}: '
          'tasks=$todayTasks, completed=$completedTasks, allCompleted=$allTasksCompleted, streak=$currentStreak');
    } catch (e) {
      AppLogger.error('Error updating day completion: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateDayCompletionForDate(
    DateTime date,
    int todayTasks,
    int completedTasks,
    bool allTasksCompleted,
  ) async {
    try {
      // Calculate current and longest streaks based on the new completion status
      final currentStreak =
          await _calculateCurrentStreak(date, allTasksCompleted);
      final existingLongestStreak =
          await _databaseService.getLongestStreakFromDB();
      final longestStreak = currentStreak > existingLongestStreak
          ? currentStreak
          : existingLongestStreak;

      final streakData = {
        'date': date.toIso8601String().split('T')[0],
        'totalTasks': todayTasks,
        'completedTasks': completedTasks,
        'allTasksCompleted': allTasksCompleted ? 1 : 0,
        'currentStreak': currentStreak,
        'longestStreak': longestStreak,
      };

      await _databaseService.insertOrUpdateStreakData(streakData);
      AppLogger.info(
          'Updated streak for ${date.toIso8601String().split('T')[0]}: '
          'tasks=$todayTasks, completed=$completedTasks, allCompleted=$allTasksCompleted, streak=$currentStreak');
    } catch (e) {
      AppLogger.error('Error updating day completion for date: $e');
      rethrow;
    }
  }

  @override
  Future<List<StreakModel>> getCompletionHistory() async {
    try {
      final streakHistory = await _databaseService.getStreakHistory();
      return streakHistory.map((data) {
        return StreakModel(
          date: DateTime.parse(data['date']),
          totalTasks: data['totalTasks'] as int,
          completedTasks: data['completedTasks'] as int,
          allTasksCompleted: (data['allTasksCompleted'] as int) == 1,
        );
      }).toList();
    } catch (e) {
      AppLogger.error('Error getting completion history: $e');
      return [];
    }
  }

  @override
  Future<int> getCurrentStreak() async {
    try {
      return await _databaseService.getCurrentStreakFromDB();
    } catch (e) {
      AppLogger.error('Error getting current streak: $e');
      return 0;
    }
  }

  @override
  Future<int> getLongestStreak() async {
    try {
      return await _databaseService.getLongestStreakFromDB();
    } catch (e) {
      AppLogger.error('Error getting longest streak: $e');
      return 0;
    }
  }

  @override
  Future<int> getTotalCompletedDays() async {
    try {
      return await _databaseService.getTotalCompletedDaysFromDB();
    } catch (e) {
      AppLogger.error('Error getting total completed days: $e');
      return 0;
    }
  }

  @override
  Future<void> resetStreak() async {
    try {
      await _databaseService.deleteStreakData();
      AppLogger.info('Streak data reset successfully');
    } catch (e) {
      AppLogger.error('Error resetting streak: $e');
      rethrow;
    }
  }

  @override
  Future<void> removeStreakForDate(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      await _databaseService.deleteStreakDataForDate(dateString);
      AppLogger.info('Removed streak for date: $dateString');
    } catch (e) {
      AppLogger.error('Error removing streak for date: $e');
      rethrow;
    }
  }

  /// Calculate current streak based on consecutive completed days
  Future<int> _calculateCurrentStreak(
      DateTime targetDate, bool isCompleted) async {
    if (!isCompleted) return 0;

    try {
      final history = await getCompletionHistory();

      // Normalize target date to remove time component
      final normalizedTargetDate =
          DateTime(targetDate.year, targetDate.month, targetDate.day);

      // If no history exists, this is the first completed day
      if (history.isEmpty) return 1;

      // Sort by date descending (most recent first)
      final sortedHistory = List<StreakModel>.from(history)
        ..sort((a, b) => b.date.compareTo(a.date));

      int streak = 1; // Start with 1 for the current day if completed

      // Count backwards for consecutive completed days
      DateTime checkDate = normalizedTargetDate.subtract(Duration(days: 1));

      while (true) {
        // Find data for the check date
        final dayData = sortedHistory.firstWhere(
          (data) => _isSameDay(data.date, checkDate),
          orElse: () => StreakModel(
            date: checkDate,
            totalTasks: 0,
            completedTasks: 0,
            allTasksCompleted: false,
          ),
        );

        // If this day had tasks and all were completed, continue streak
        if (dayData.totalTasks > 0 && dayData.allTasksCompleted) {
          streak++;
          checkDate = checkDate.subtract(Duration(days: 1));
        } else if (dayData.totalTasks > 0) {
          // Day had tasks but not all completed - streak breaks
          break;
        } else {
          // No tasks for this day - continue checking previous days
          checkDate = checkDate.subtract(Duration(days: 1));
        }

        // Safety check to prevent infinite loops
        if (checkDate.isBefore(DateTime.now().subtract(Duration(days: 365)))) {
          break;
        }
      }

      return streak;
    } catch (e) {
      AppLogger.error('Error calculating current streak: $e');
      return isCompleted ? 1 : 0;
    }
  }

  /// Check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
